package com.doudian.open.api.logistics_queryPackageRoute;

import java.util.*;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.logistics_queryPackageRoute.data.*;

//auto generated, do not edit

public class LogisticsQueryPackageRouteResponse extends DoudianOpResponse<Data> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}