package com.doudian.open.api.buyin_allKolStrategyCreate;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.buyin_allKolStrategyCreate.data.*;

//auto generated, do not edit

public class BuyinAllKolStrategyCreateResponse extends DoudianOpResponse<BuyinAllKolStrategyCreateData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}