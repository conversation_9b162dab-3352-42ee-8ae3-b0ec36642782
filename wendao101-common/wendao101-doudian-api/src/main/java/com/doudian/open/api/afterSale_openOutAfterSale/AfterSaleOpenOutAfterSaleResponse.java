package com.doudian.open.api.afterSale_openOutAfterSale;

import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;
import com.doudian.open.api.afterSale_openOutAfterSale.data.*;

//auto generated, do not edit

public class AfterSaleOpenOutAfterSaleResponse extends DoudianOpResponse<AfterSaleOpenOutAfterSaleData> {



	@Override
	public String toString(){
		return JsonUtil.toJson(this);
	}

}