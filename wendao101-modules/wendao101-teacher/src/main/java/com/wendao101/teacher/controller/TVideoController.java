package com.wendao101.teacher.controller;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.DescribeMediaInfosRequest;
import com.tencentcloudapi.vod.v20180717.models.DescribeMediaInfosResponse;
import com.tencentcloudapi.vod.v20180717.models.MediaInfo;
import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.teacher.domain.Course;
import com.wendao101.teacher.domain.CourseDirectory;
import com.wendao101.teacher.domain.TTeacher;
import com.wendao101.teacher.domain.TVideo;
import com.wendao101.teacher.dto.ChangeVideoNameDTO;
import com.wendao101.teacher.dto.QueryVideoDTO;
import com.wendao101.teacher.dto.VideoFinishDTO;
import com.wendao101.teacher.resultdto.VideoTransResultDTO;
import com.wendao101.teacher.service.ICourseDirectoryService;
import com.wendao101.teacher.service.ICourseService;
import com.wendao101.teacher.service.ITTeacherService;
import com.wendao101.teacher.service.ITVideoService;
import com.wendao101.wendao.log.annotation.WenDaoLog;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 素材视频Controller
 * 
 * <AUTHOR>
 * @date 2023-07-25
 */
@RestController
@RequestMapping("/video")
public class TVideoController extends BaseController {
    @Value("${wendao.video.vodsubAppid}")
    private String vodsubAppid;
    @Value("${wendao.video.secretId}")
    private String secretId;
    @Value("${wendao.video.secretKey}")
    private String secretKey;
    @Autowired
    private ITVideoService tVideoService;

    @Autowired
    private ITTeacherService teacherService;

    @Autowired
    private ICourseDirectoryService courseDirectoryService;

    @Autowired
    private ICourseService courseService;
    /**
     * 上传视频完毕后写入数据库记录
     *
     * @param videoFinishDTO
     * @return
     */
    @WenDaoLog(title = "素材库",subTitle = "视频",businessType = "上传视频素材")
    @PostMapping("/upload_finish")
    public AjaxResult uploadFinish(@RequestBody VideoFinishDTO videoFinishDTO, HttpServletRequest request) {
        Long teacherId = SecurityUtils.getUserId();
        TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(teacherId);
        // 先查找先相关fileId的记录是否已经存在，如果存在则报错
        TVideo tVideo = new TVideo();
        String fileId = videoFinishDTO.getFileId();
        tVideo.setTcvodFileId(fileId);
        List<TVideo> tVideos = tVideoService.selectTVideoList(tVideo);
        if (CollectionUtils.isNotEmpty(tVideos)) {
            return AjaxResult.error("视频已经存在，请勿重复上传");
        }
        //插入视频记录
        tVideo.setFileName(videoFinishDTO.getFileName());
        tVideo.setTcvodMediaUrlBeforeTrans(videoFinishDTO.getUrl());
        tVideo.setTcvodFileId(videoFinishDTO.getFileId());
        tVideo.setTeacherId(teacherId);
        tVideo.setFileOriginalSize(videoFinishDTO.getFileSize());
        tVideo.setAppNameType(tTeacher.getAppNameType());
        int row = tVideoService.insertTVideo(tVideo);
        //查询腾讯视频时长
        Long duration = getMediaDuration(videoFinishDTO.getFileId());
        videoFinishDTO.setDuration(duration);
        if (row > 0) {
            Long id = tVideo.getId();
            videoFinishDTO.setId(id);
            return AjaxResult.success(videoFinishDTO);
        } else {
            return AjaxResult.error();
        }
    }

    private Long getMediaDuration(String fileId){
        try{
            Credential cred = new Credential(secretId, secretKey);
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("vod.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            VodClient client = new VodClient(cred, "", clientProfile);
            DescribeMediaInfosRequest req = new DescribeMediaInfosRequest();
            String[] fileIds1 = {fileId};
            req.setFileIds(fileIds1);
            req.setSubAppId(new Long(vodsubAppid));

            String[] filters1 = {"metaData"};
            req.setFilters(filters1);
            DescribeMediaInfosResponse resp = client.DescribeMediaInfos(req);
            if(resp==null){
                return 0L;
            }
            if(resp.getMediaInfoSet()==null||resp.getMediaInfoSet().length==0){
                return 0L;
            }
            MediaInfo mediaInfo = resp.getMediaInfoSet()[0];
            if(mediaInfo==null){
                return 0L;
            }
            if(mediaInfo.getMetaData()==null){
                return 0L;
            }
            Float duration = mediaInfo.getMetaData().getDuration();
            if(duration==null){
                return 0L;
            }
            return duration.longValue();
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }
        return 0L;
    }

    /**
     * 视频转码状态查询
     */
    @GetMapping("/query_trans_status")
    public AjaxResult queryTransStatus(@RequestParam Long id)
    {
        TVideo tVideo = tVideoService.selectTVideoById(id);
        if(tVideo==null){
            return AjaxResult.error("该id记录不存在");
        }
        VideoTransResultDTO result  = new VideoTransResultDTO();
        result.setId(tVideo.getId());
        result.setTranscodeStatus(tVideo.getTranscodeStatus());
        return AjaxResult.success(result);
    }

    /**
     * 查询素材视频列表
     */
    @GetMapping("/list")
    public TableDataInfo list(QueryVideoDTO queryVideoDTO)
    {
        List<TVideo> list = null;
        Long teacherId = SecurityUtils.getUserId();
        startPage();
        TVideo tVideo = new TVideo();
        // 限制查询范围为当前老师
        tVideo.setTeacherId(teacherId);
        tVideo.setIsDelete(0);
        if(queryVideoDTO!=null&&StringUtils.isNotEmpty(queryVideoDTO.getQueryVideoName())){
            tVideo.setFileName(queryVideoDTO.getQueryVideoName());
        }
        if(queryVideoDTO.getGroupId()==null|| queryVideoDTO.getGroupId() ==0){
            list = tVideoService.selectTVideoList(tVideo);
        }else{
            queryVideoDTO.setIsDelete(0);
            list = tVideoService.selectTVideoListByGroup(queryVideoDTO);
        }
        return getDataTable(list);
    }
    /**
     * 修改素材视频
     */
    @WenDaoLog(title = "素材库",subTitle = "视频",businessType = "修改视频素材")
    @PutMapping("/change_video_name")
    public AjaxResult changeVideoName(@RequestBody ChangeVideoNameDTO changeVideoNameDTO,HttpServletRequest request)
    {
        Long teacherId = SecurityUtils.getUserId();
        if(changeVideoNameDTO==null||StringUtils.isBlank(changeVideoNameDTO.getFileName())||changeVideoNameDTO.getId()==null){
            return error("参数错误");
        }
        return toAjax(tVideoService.updateTVideoNameByIdAndTeacherId(teacherId,changeVideoNameDTO.getId(),changeVideoNameDTO.getFileName()));
    }

    /**
     * 新删除素材视频
     */
    @WenDaoLog(title = "素材库", subTitle = "视频", businessType = "新删除视频素材")
    @DeleteMapping("/newDelete/{ids}")
    public AjaxResult newRemove(@PathVariable Long[] ids, HttpServletRequest request) {
        Long teacherId = SecurityUtils.getUserId();
        if (ids == null || ids.length == 0) {
            return error("参数错误");
        }
        Map<Long, TVideo> cannotDeleteMapIdKey = new HashMap<>();
        //按ids先查出所有path_url不为空的记录
        List<TVideo> list = tVideoService.selectTVideoListByIds(ids, teacherId);
        //处理判断是否可以删除的逻辑,如果在该老师未删除课程的任意目录中存在则不能删除,并放入cannotDeleteMapIdKey中
        Set<Long> cannotDelete = cannotDeleteMapIdKey.keySet();
        //将ids转换为set
        Set<Long> idsSet = new HashSet<>(Arrays.asList(ids));
        //用iterator遍历idsSet，如果idsSet中的元素在cannotDelete中存在，则从idsSet中删除该元素
        idsSet.removeIf(cannotDelete::contains);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("successCount", 0);
        resultMap.put("failCount", cannotDelete.size());
        resultMap.put("failList", cannotDeleteMapIdKey.values());
        if (!idsSet.isEmpty()) {
            ids = idsSet.toArray(new Long[0]);
            int i = tVideoService.deleteTVideoByIds(ids, teacherId);
            resultMap.put("successCount", i);
        }
        return AjaxResult.success(resultMap);
    }

    /**
     * 删除素材视频
     */
    @WenDaoLog(title = "素材库",subTitle = "视频",businessType = "删除视频素材")
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids,HttpServletRequest request)
    {
        Long teacherId = SecurityUtils.getUserId();
        if(ids==null||ids.length==0){
            return error("参数错误");
        }
        return toAjax(tVideoService.deleteTVideoByIds(ids,teacherId));
    }


}
